'use client';
import React from 'react';
import { DataTableColumnHeader } from '@/components/table/data-table-column-header';
import { ExpressionTestResult } from '@/types';
import { ColumnDef } from '@tanstack/react-table';
import Link from 'next/link';
import format from 'date-fns/format';
import { DataTable } from '@/components/table/data-table';

type ExpressionTestResultWithExamen = ExpressionTestResult & {
  examen: string;
};

interface ExpressionListProps {
  data: ExpressionTestResultWithExamen[];
}
function ExpressionList({ data }: ExpressionListProps) {
  return (
    <div className="">
      <DataTable
        showToolbar
        filteredColumn="email"
        columns={correctionColumns}
        data={data}
        sortOptions={[
          {
            id: 'createdAt',
            desc: false,
          },
        ]}
      />
    </div>
  );
}

export default ExpressionList;

export const correctionColumns: ColumnDef<ExpressionTestResultWithExamen>[] = [
  {
    accessorKey: 'serie',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Serie" />
    ),
    cell: ({ row }) => {
      return (
        <Link
          href={`/dashboard/administration/correction/${row.original.examen}/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              Série {row.original.serie.libelle}
            </span>
          </div>
        </Link>
      );
    },
  },

  {
    accessorFn: (row) => row.user.email,
    id: 'email',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
    cell: ({ row }) => {
      return (
        <Link
          href={`/dashboard/administration/correction/${row.original.examen}/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              {row.original.user.email}
            </span>
          </div>
        </Link>
      );
    },
  },
  {
    accessorKey: 'createdAt',
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Fait le" />
    ),
    cell: ({ row }) => {
      return (
        <Link
          href={`/dashboard/administration/correction/${row.original.examen}/${row.original.type}/${row.original._id}`}
          title="Faire la correction"
        >
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              {format(
                new Date(row.getValue('createdAt')),
                "dd/MM/yyyy' 'HH:mm:ss",
              )}
            </span>
          </div>
        </Link>
      );
    },
  },
];
