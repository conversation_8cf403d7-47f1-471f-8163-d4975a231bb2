'use client';
import { ExpressionTestResult } from '@/types';
import React from 'react';
import { correctionColumns } from './column';
import { DataTable } from '@/components/table/data-table';
interface ExpressionListProps {
  data: ExpressionTestResult[];
}
function ExpressionList({ data }: ExpressionListProps) {
  return (
    <div className="">
      <DataTable
        showToolbar
        filteredColumn="name"
        columns={correctionColumns}
        data={data}
        sortOptions={[
          {
            id: 'createdAt',
            desc: false,
          },
        ]}
      />
    </div>
  );
}

export default ExpressionList;
