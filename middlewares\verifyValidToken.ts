import { getToken } from 'next-auth/jwt';
import {
  NextFetchEvent,
  NextMiddleware,
  NextRequest,
  NextResponse,
} from 'next/server';
import { isPublicPath } from './helpers';




export function verifyValidToken(middleware: NextMiddleware) {
  return async (req: NextRequest, event: NextFetchEvent) => {
    const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });

   if(isPublicPath(req.nextUrl.pathname)){
      return NextResponse.next();
    }

    




   

  

    return middleware(req, event);
  };
}



