'use client';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { StoragePayload, useCorrectionStore } from '@/context/correction';
import { useParams } from 'next/navigation';
import { useEffect } from 'react';

function TaskSelector() {
  const { correctionId } = useParams();
  // Sélectionner uniquement les propriétés nécessaires
  const currentTask = useCorrectionStore((state) => state.currentTask);
  const noteTaskOne = useCorrectionStore((state) => state.noteTaskOne);
  const noteTaskTwo = useCorrectionStore((state) => state.noteTaskTwo);
  const commentTaskOne = useCorrectionStore((state) => state.commentTaskOne);
  const commentTaskTwo = useCorrectionStore((state) => state.commentTaskTwo);

  const uploadPreviousData = useCorrectionStore(
    (state) => state.uploadPreviousData,
  );

  useEffect(() => {
    const isDirty =
      noteTaskOne !== undefined ||
      noteTaskTwo !== undefined ||
      commentTaskOne !== undefined ||
      commentTaskTwo !== undefined;

    if (isDirty) {
      const payload = {
        currentTask,
        noteTaskOne,
        noteTaskTwo,
        commentTaskOne,
        commentTaskTwo,
      };
      localStorage.setItem(
        `correction-${correctionId}`,
        JSON.stringify(payload),
      );
    }
  }, [
    correctionId,
    currentTask,
    noteTaskOne,
    noteTaskTwo,
    commentTaskOne,
    commentTaskTwo,
  ]);
  useEffect(() => {
    const stringP = localStorage.getItem(`correction-${correctionId}`);
    if (!stringP) return;

    const payload = JSON.parse(stringP) as StoragePayload;
    uploadPreviousData(payload);
  }, [correctionId, uploadPreviousData]);

  const setCurrentTask = useCorrectionStore((state) => state.setCurrentTask);
  return (
    <div className="">
      <Tabs
        value={currentTask.toString()}
        onValueChange={(value) => {
          setCurrentTask(parseInt(value));
        }}
        className="h-fit w-full"
      >
        <TabsList className="!relative !gap-3">
          <TabsTrigger
            value="1"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 1
          </TabsTrigger>
          <TabsTrigger
            value="2"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 2
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}

export default TaskSelector;
