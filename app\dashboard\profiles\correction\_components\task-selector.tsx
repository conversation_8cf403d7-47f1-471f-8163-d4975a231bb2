'use client';
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { useCorrectionStore } from '@/context/correction';

function TaskSelector() {
  const setCurrentTask = useCorrectionStore((state) => state.setCurrentTask);
  const current = useCorrectionStore((state) => state.currentTask);
  return (
    <div className="">
      <Tabs
        value={current.toString()}
        onValueChange={(value) => {
          setCurrentTask(parseInt(value));
        }}
        className="h-fit w-full"
      >
        <TabsList className="!relative !gap-3">
          <TabsTrigger
            value="1"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 1
          </TabsTrigger>
          <TabsTrigger
            value="2"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 2
          </TabsTrigger>

          <TabsTrigger
            value="3"
            className={`font-normal tracking-wider data-[state=active]:!bg-blue-500 data-[state=active]:!font-semibold data-[state=active]:!text-white`}
          >
            Tâche 3
          </TabsTrigger>
        </TabsList>
      </Tabs>
    </div>
  );
}

export default TaskSelector;
